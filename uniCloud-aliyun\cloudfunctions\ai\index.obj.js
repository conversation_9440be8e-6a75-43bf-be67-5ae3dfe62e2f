/**
 * AI 云对象主模块
 *
 * 提供智能任务执行和流式聊天功能：
 * - 任务意图识别和执行计划生成
 * - 多步骤任务的自动化执行
 * - 实时流式聊天响应
 * - Todo 工具集成和测试功能
 * - 执行上下文管理和数据传递
 */

/**
 * 执行上下文管理器
 * 用于管理任务执行过程中的所有数据和状态信息
 *
 * 数据结构：
 * - stepResults: Map<stepId, {result, metadata}> 存储步骤执行结果
 * - contextData: Map<key, value> 存储提取的上下文数据
 * - metadata: Object 存储执行过程的元数据信息
 *
 * 核心功能：
 * - 步骤结果管理：存储和检索每个步骤的执行结果
 * - 上下文数据提取：根据 AI 提取的实体，从结果中提取关键信息
 * - 项目智能匹配：基于 AI 提取的项目名称，匹配最相关的项目
 */
const { OpenAI } = require('openai')
const {
  FUNCTION_TOOLS,
  doubaoParams,
  SSE_MESSAGE_TYPES,
  createSSEMessage,
  generateSessionId,
} = require('./modules/config.js')
const TodoTool = require('./modules/todo')

/**
 * 流式工具调用处理（豆包模型优化版）
 */
async function handleStreamResponse(streamResponse, sseChannel, sessionId, originalMessages) {
  let pendingToolCalls = [] // 使用数组存储工具调用，支持索引
  let assistantMessage = ''
  let hasToolCalls = false

  for await (const chunk of streamResponse) {
    const delta = chunk.choices[0]?.delta
    const finishReason = chunk.choices[0]?.finish_reason

    // 处理普通文本内容
    if (delta?.content) {
      assistantMessage += delta.content
      await sseChannel.write(
        createSSEMessage(SSE_MESSAGE_TYPES.CHAT_CONTENT_CHUNK, sessionId, {
          content: delta.content,
          isComplete: false,
        })
      )
    }

    // 处理工具调用 - 豆包模型增量式处理
    if (delta?.tool_calls) {
      hasToolCalls = true

      for (const toolCallDelta of delta.tool_calls) {
        const index = toolCallDelta.index || 0
        const toolCallId = toolCallDelta.id

        // 初始化工具调用对象
        if (!pendingToolCalls[index]) {
          pendingToolCalls[index] = {
            id: toolCallId || `call_${Date.now()}_${index}`,
            type: 'function',
            function: {
              name: toolCallDelta.function?.name || '',
              arguments: toolCallDelta.function?.arguments || '',
            },
          }

          // 推送工具调用开始消息
          if (toolCallDelta.function?.name) {
            await sseChannel.write(
              createSSEMessage(SSE_MESSAGE_TYPES.TOOL_CALL_START, sessionId, {
                toolName: toolCallDelta.function.name,
                toolCallId: pendingToolCalls[index].id,
              })
            )
          }
        } else {
          // 累积工具调用参数
          if (toolCallDelta.function?.name) {
            pendingToolCalls[index].function.name = toolCallDelta.function.name
          }
          if (toolCallDelta.function?.arguments) {
            pendingToolCalls[index].function.arguments += toolCallDelta.function.arguments
          }
        }
      }
    }

    // 检查是否完成工具调用
    if (finishReason === 'tool_calls' && hasToolCalls) {
      // 执行所有完整的工具调用
      const toolResults = []

      for (const toolCall of pendingToolCalls.filter((tc) => tc && tc.function.name)) {
        try {
          const result = await executeToolCall(toolCall, sseChannel, sessionId)
          toolResults.push({
            toolCall: toolCall,
            result: result,
          })
        } catch (error) {
          console.error('工具调用执行失败：', error.message)
          await sseChannel.write(
            createSSEMessage(SSE_MESSAGE_TYPES.TOOL_EXECUTION_ERROR, sessionId, {
              toolName: toolCall.function.name,
              error: error.message,
            })
          )

          // 即使失败也要记录，以便后续处理
          toolResults.push({
            toolCall: toolCall,
            result: { error: error.message, success: false },
          })
        }
      }

      // 继续对话，让模型基于工具结果生成最终回复
      if (toolResults.length > 0) {
        await continueConversationWithToolResults(
          originalMessages,
          pendingToolCalls.filter((tc) => tc),
          toolResults,
          sseChannel,
          sessionId
        )
      }
    }

    // 处理普通对话结束
    if (finishReason === 'stop' && !hasToolCalls) {
      await sseChannel.write(
        createSSEMessage(SSE_MESSAGE_TYPES.CHAT_CONTENT_CHUNK, sessionId, {
          content: '',
          isComplete: true,
        })
      )
    }
  }
}

/**
 * 继续对话 - 将工具调用结果传回模型
 */
async function continueConversationWithToolResults(originalMessages, toolCalls, toolResults, sseChannel, sessionId) {
  const openai = new OpenAI(doubaoParams)

  // 构建包含工具调用和结果的完整消息历史
  const messagesWithToolResults = [
    ...originalMessages,
    // 添加助手的工具调用消息
    {
      role: 'assistant',
      content: null,
      tool_calls: toolCalls,
    },
    // 添加工具执行结果消息
    ...toolResults.map(({ toolCall, result }) => ({
      role: 'tool',
      tool_call_id: toolCall.id,
      content: JSON.stringify(result),
    })),
  ]

  // 推送工具结果处理开始消息
  await sseChannel.write(
    createSSEMessage(SSE_MESSAGE_TYPES.TOOL_RESULT_PROCESSING, sessionId, {
      message: '正在基于工具执行结果生成回复...',
    })
  )

  try {
    const followUpResponse = await openai.chat.completions.create({
      model: 'doubao-seed-1-6-flash-250715',
      messages: messagesWithToolResults,
      stream: true,
      timeout: 60000,
      thinking: { type: 'disabled' },
    })

    // 处理后续回复
    for await (const chunk of followUpResponse) {
      const delta = chunk.choices[0]?.delta
      const finishReason = chunk.choices[0]?.finish_reason

      if (delta?.content) {
        await sseChannel.write(
          createSSEMessage(SSE_MESSAGE_TYPES.CHAT_CONTENT_CHUNK, sessionId, {
            content: delta.content,
            isComplete: false,
          })
        )
      }

      if (finishReason === 'stop') {
        await sseChannel.write(
          createSSEMessage(SSE_MESSAGE_TYPES.CHAT_CONTENT_CHUNK, sessionId, {
            content: '',
            isComplete: true,
          })
        )
        break
      }
    }
  } catch (error) {
    console.error('工具结果处理失败：', error)
    await sseChannel.write(
      createSSEMessage(SSE_MESSAGE_TYPES.TOOL_RESULT_ERROR, sessionId, {
        error: '基于工具结果生成回复失败',
        details: error.message,
      })
    )
  }
}

/**
 * 统一工具执行接口
 */
async function executeToolCall(toolCall, sseChannel, sessionId) {
  const { function: func } = toolCall
  const toolName = func.name

  try {
    const parameters = JSON.parse(func.arguments)

    // 推送工具执行开始消息
    await sseChannel.write(
      createSSEMessage(SSE_MESSAGE_TYPES.TOOL_EXECUTION_START, sessionId, {
        toolName: toolName,
        parameters: parameters,
      })
    )

    let result
    switch (toolName) {
      case 'getTasks':
        result = await executeGetTasks(parameters)
        break
      case 'createTask':
        result = await executeCreateTask(parameters)
        break
      case 'getProjects':
        result = await executeGetProjects(parameters)
        break
      case 'updateTask':
        result = await executeUpdateTask(parameters)
        break
      case 'getCurrentTimeInfo':
        result = await executeGetCurrentTimeInfo(parameters)
        break
      default:
        throw new Error(`未知的工具：${toolName}`)
    }

    // 推送工具执行完成消息
    await sseChannel.write(
      createSSEMessage(SSE_MESSAGE_TYPES.TOOL_EXECUTION_COMPLETE, sessionId, {
        toolName: toolName,
        result: result,
        success: true,
        toolCallId: toolCall.id
      })
    )

    return result
  } catch (error) {
    // 推送工具执行失败消息
    await sseChannel.write(
      createSSEMessage(SSE_MESSAGE_TYPES.TOOL_EXECUTION_ERROR, sessionId, {
        toolName: toolName,
        error: error.message,
        success: false,
      })
    )

    throw error
  }
}

module.exports = {
  async chatStreamSSE({ channel, message, messages: history_records }) {
    const sessionId = generateSessionId()

    // 参数验证
    if (!message) {
      return {
        errCode: 'PARAM_IS_NULL',
        errMsg: '消息内容不能为空',
      }
    }

    if (!channel) {
      return {
        errCode: 'PARAM_IS_NULL',
        errMsg: 'SSE Channel 不能为空',
      }
    }

    try {
      const sseChannel = uniCloud.deserializeSSEChannel(channel)

      // 推送开始处理消息
      await sseChannel.write(
        createSSEMessage(SSE_MESSAGE_TYPES.PROCESSING_START, sessionId, {
          message: '开始处理您的请求...',
        })
      )

      // 初始化 AI 客户端
      const openai = new OpenAI(doubaoParams)

      // 🎯 获取当前真实时间信息
      const now = new Date()
      const timeInfo = {
        current_datetime: now.toISOString(),
        current_date: now.toLocaleDateString('zh-CN'),
        current_time: now.toLocaleTimeString('zh-CN'),
        current_year: now.getFullYear(),
        current_month: now.getMonth() + 1,
        current_day: now.getDate(),
        current_hour: now.getHours(),
        current_weekday: ['周日', '周一', '周二', '周三', '周四', '周五', '周六'][now.getDay()],
        timezone: 'Asia/Shanghai (UTC+8)',
      }

      // 计算相对时间
      const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000)
      const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000)
      const dayAfterTomorrow = new Date(now.getTime() + 2 * 24 * 60 * 60 * 1000)
      const dayBeforeYesterday = new Date(now.getTime() - 2 * 24 * 60 * 60 * 1000)

      // 计算本周和下周的日期
      const currentWeekday = now.getDay() // 0=周日，1=周一，..., 6=周六
      const daysToSaturday = 6 - currentWeekday
      const daysToSunday = 7 - currentWeekday
      const thisSaturday = new Date(now.getTime() + daysToSaturday * 24 * 60 * 60 * 1000)
      const thisSunday = new Date(now.getTime() + daysToSunday * 24 * 60 * 60 * 1000)

      // 计算下周一
      const daysToNextMonday = (8 - currentWeekday) % 7 || 7
      const nextMonday = new Date(now.getTime() + daysToNextMonday * 24 * 60 * 60 * 1000)

      // 计算本月和下月
      const currentMonth = now.getMonth()
      const currentYear = now.getFullYear()
      const lastDayOfMonth = new Date(currentYear, currentMonth + 1, 0)
      const firstDayOfNextMonth = new Date(currentYear, currentMonth + 1, 1)
      const lastDayOfNextMonth = new Date(currentYear, currentMonth + 2, 0)

      // 构建消息数组 - 正确处理历史消息格式
      const messages = [
        {
          role: 'system',
          content: `你是一个专业的任务管理助手，可以帮助用户精确管理任务和项目。

🕒 【当前真实时间信息】
- 当前日期时间：${timeInfo.current_datetime}
- 当前日期：${timeInfo.current_date}
- 当前时间：${timeInfo.current_time}
- 当前年份：${timeInfo.current_year}
- 当前月份：${timeInfo.current_month}月
- 当前日期：${timeInfo.current_day}日
- 当前星期：${timeInfo.current_weekday}
- 时区：${timeInfo.timezone}

⏰ 【时间解析规则】基于上述真实时间进行准确转换：
- "今天" = ${timeInfo.current_date}
- "明天" = ${tomorrow.toLocaleDateString('zh-CN')}
- "后天" = ${dayAfterTomorrow.toLocaleDateString('zh-CN')}
- "昨天" = ${yesterday.toLocaleDateString('zh-CN')}
- "前天" = ${dayBeforeYesterday.toLocaleDateString('zh-CN')}
- "今晚" = ${timeInfo.current_date} 23:59:59
- "明天早上" = ${tomorrow.toLocaleDateString('zh-CN')} 09:00:00
- "明天晚上" = ${tomorrow.toLocaleDateString('zh-CN')} 23:59:59
- "本周末" = ${thisSaturday.toLocaleDateString('zh-CN')} (周六)
- "这个周末" = ${thisSunday.toLocaleDateString('zh-CN')} (周日)
- "下周一" = ${nextMonday.toLocaleDateString('zh-CN')}
- "本月底" = ${lastDayOfMonth.toLocaleDateString('zh-CN')} 23:59:59
- "下个月初" = ${firstDayOfNextMonth.toLocaleDateString('zh-CN')} 09:00:00
- "下月底" = ${lastDayOfNextMonth.toLocaleDateString('zh-CN')} 23:59:59

📝 【任务创建时间表达处理规则】
当用户输入包含时间表达的任务创建请求时，必须遵循以下处理规则：

1. **时间表达识别与提取**：
   - 识别用户输入中的时间关键词：今天、明天、后天、昨天、前天
   - 识别周期表达：本周、下周、上周、本月、下月、上月
   - 识别具体时间段：早上、上午、中午、下午、晚上、深夜
   - 识别星期表达：周一、周二...周日、星期一、星期二...星期日

2. **任务标题清理规则**：
   - 从任务标题中移除已识别的时间关键词
   - 移除多余的连接词：要、需要、准备、计划、安排等
   - 保持标题简洁明确，突出核心任务内容

3. **时间参数转换规则**：
   - 将识别的时间表达转换为对应的 dueDate 或 startDate 参数
   - 根据时间精度选择合适的格式：
     * 包含具体时间段：使用 YYYY-MM-DD HH:MM:SS 格式
     * 仅包含日期：使用 YYYY-MM-DD 格式（全天任务）
   - 具体时间段对应时间：早上09:00，上午10:00，中午12:00，下午15:00，晚上18:00，深夜23:59

4. **时间精度处理规则**：
   - **包含具体时间段的表达**：设置具体时间
     * "今天下午开会" → title="开会", dueDate="${timeInfo.current_date} 15:00:00"
     * "明天晚上聚餐" → title="聚餐", dueDate="${tomorrow.toLocaleDateString('zh-CN')} 23:59:59"
     * "后天早上出发" → title="出发", dueDate="${dayAfterTomorrow.toLocaleDateString('zh-CN')} 09:00:00"

   - **仅包含日期的表达**：使用日期格式（全天任务）
     * "今天开会" → title="开会", dueDate="${timeInfo.current_date}"
     * "明天提交报告" → title="提交报告", dueDate="${tomorrow.toLocaleDateString('zh-CN')}"
     * "后天完成任务" → title="完成任务", dueDate="${dayAfterTomorrow.toLocaleDateString('zh-CN')}"

5. **处理示例对比**：
   ✅ 正确处理：
   - 输入："添加任务：明天要开股东大会"
   - 解析：title="开股东大会", dueDate="${tomorrow.toLocaleDateString('zh-CN')}" (全天任务)

   - 输入："添加任务：明天下午要开股东大会"
   - 解析：title="开股东大会", dueDate="${tomorrow.toLocaleDateString('zh-CN')} 15:00:00" (具体时间)

   - 输入："添加任务：本月完成学习计划"
   - 解析：title="完成学习计划", dueDate="${lastDayOfMonth.toLocaleDateString('zh-CN')}" (全天任务)

   - 输入："明天早上9点开会"
   - 解析：title="开会", dueDate="${tomorrow.toLocaleDateString('zh-CN')} 09:00:00" (具体时间)

   ❌ 错误处理：
   - 不要将时间词包含在标题中：title="明天要开股东大会"
   - 不要忽略时间信息而不设置 dueDate
   - 不要为仅有日期的表达添加默认具体时间

6. **复杂时间表达处理**：
   - **包含时间段的复杂表达**：
     * "下周三下午"：计算具体日期 + 15:00:00
     * "本月底晚上"：当月最后一天 23:59:59
     * "下个月初早上"：下月第一天 09:00:00
     * "这个周末晚上"：本周六 23:59:59

   - **仅包含日期的复杂表达**：
     * "下周三"：计算具体日期（全天任务）
     * "本月底"：当月最后一天（全天任务）
     * "下个月初"：下月第一天（全天任务）
     * "这个周末"：本周六（全天任务）

🎯 【重要指示】
1. 当用户提到相对时间（如"今天"、"明天"、"今晚"等），必须基于上述真实时间信息进行准确转换
2. 绝对不要猜测时间，始终使用提供的真实时间信息
3. **时间精度处理**：
   - 包含具体时间段（早上、下午、晚上等）：使用 YYYY-MM-DD HH:MM:SS 格式
   - 仅包含日期（今天、明天、后天等）：使用 YYYY-MM-DD 格式（全天任务）
4. **必须从任务标题中移除时间关键词，保持标题简洁**
5. **不要为仅有日期的表达自动添加默认具体时间**
6. 当用户需要执行具体操作时，请调用相应的工具函数
7. 对于一般性问题，可以直接回答

💬 【回复风格要求】
1. 保持简洁自然的对话风格，避免过度详细的技术信息
2. 任务创建成功后，不要重复显示任务 ID、创建时间等技术细节
3. 使用友好的语言确认操作结果：
   - 包含具体时间：如"任务'开股东大会'已创建成功，截止时间设为明天下午3点！"
   - 全天任务：如"任务'提交报告'已创建成功，截止日期设为明天！"
4. 避免冗长的列表格式，优先使用自然语言表达

你现在拥有准确的时间感知能力和智能的时间表达处理能力，可以精确处理所有时间相关的任务管理需求！`,
        },
        // 正确处理历史消息，保留工具调用相关信息
        ...history_records.map((msg) => ({
          role: msg.role,
          content: msg.content,
          // 保留工具调用相关信息
          ...(msg.tool_calls && { tool_calls: msg.tool_calls }),
          ...(msg.tool_call_id && { tool_call_id: msg.tool_call_id }),
        })),
        {
          role: 'user',
          content: message,
        },
      ]

      // 创建流式响应
      const streamResponse = await openai.chat.completions.create({
        model: 'doubao-seed-1-6-flash-250715',
        messages: messages,
        tools: FUNCTION_TOOLS,
        tool_choice: 'auto',
        stream: true,
        timeout: 60000,
        thinking: { type: 'disabled' },
      })

      // 处理流式响应 - 传入原始消息用于工具调用后续处理
      await handleStreamResponse(streamResponse, sseChannel, sessionId, messages)

      // 推送会话结束消息
      await sseChannel.end(
        createSSEMessage(SSE_MESSAGE_TYPES.SESSION_END, sessionId, {
          message: '处理完成',
        })
      )

      return {
        errCode: 0,
        errMsg: 'success',
        data: {
          type: 'function_calling_complete',
          sessionId: sessionId,
        },
      }
    } catch (error) {
      console.error('chatStreamSSE 异常：', error.message)

      // 尝试推送错误消息
      try {
        const sseChannel = uniCloud.deserializeSSEChannel(channel)
        await sseChannel.end(
          createSSEMessage(SSE_MESSAGE_TYPES.ERROR, sessionId, {
            error: error.message,
            timestamp: new Date().toISOString(),
          })
        )
      } catch (channelError) {
        console.error('SSE 推送错误：', channelError)
      }

      return {
        errCode: 'SYSTEM_ERROR',
        errMsg: error.message || '系统处理失败',
        data: {
          type: 'system_error',
          sessionId: sessionId,
        },
      }
    }
  },
}

// 获取任务列表
async function executeGetTasks(parameters) {
  console.log('🔍 [executeGetTasks] === 开始获取任务列表 ===')
  console.log('📋 [executeGetTasks] 接收到的参数：', JSON.stringify(parameters, null, 2))

  const todoTool = new TodoTool()

  // 构建 todo 模块的查询参数
  const options = {
    mode: 'all', // 默认获取所有任务
    keyword: parameters.keyword || null,
    completed: parameters.completed,
    projectName: parameters.projectName || null,
    priority: parameters.priority || null,
  }

  console.log('⚙️ [executeGetTasks] 构建的查询参数：', JSON.stringify(options, null, 2))

  console.log('🚀 [executeGetTasks] 调用 todoTool.getTasks...')
  const result = await todoTool.getTasks(options)

  console.log('📦 [executeGetTasks] todoTool.getTasks 原始返回结果：')
  console.log('   - errCode:', result.errCode)
  console.log('   - errMsg:', result.errMsg)
  console.log('   - data 类型：', typeof result.data)
  console.log('   - data 是否为数组：', Array.isArray(result.data))
  console.log('   - data 长度：', result.data ? result.data.length : 'undefined')
  console.log('   - 完整 data:', JSON.stringify(result.data, null, 2))

  if (result.errCode !== null && result.errCode !== 0) {
    console.error('❌ [executeGetTasks] 获取任务失败：', result.errMsg)
    throw new Error(`获取任务失败：${result.errMsg}`)
  }

  // 处理返回的任务数据
  let tasks = result.data || []
  console.log('📊 [executeGetTasks] 处理后的任务数组长度：', tasks.length)

  // 限制返回数量
  if (parameters.limit && parameters.limit > 0) {
    const originalLength = tasks.length
    tasks = tasks.slice(0, parameters.limit)
    console.log(`✂️ [executeGetTasks] 应用 limit ${parameters.limit}: ${originalLength} -> ${tasks.length}`)
  }

  const finalResult = {
    success: true,
    data: tasks,
    message: `成功获取 ${tasks.length} 个任务`,
  }

  console.log('🎯 [executeGetTasks] 最终返回结果：')
  console.log('   - success:', finalResult.success)
  console.log('   - data 长度：', finalResult.data.length)
  console.log('   - message:', finalResult.message)
  console.log(
    '   - 任务列表概览：',
    finalResult.data.map((task) => ({
      _id: task._id,
      title: task.title,
      status: task.status,
    }))
  )
  console.log('✅ [executeGetTasks] === 获取任务列表完成 ===')

  return finalResult
}

// 创建任务
async function executeCreateTask(parameters) {
  console.log('📝 [executeCreateTask] === 开始创建任务 ===')
  console.log('📋 [executeCreateTask] 接收到的参数：', JSON.stringify(parameters, null, 2))

  const todoTool = new TodoTool()

  // 🎯 智能处理时间信息
  const now = new Date()
  let processedStartDate = parameters.startDate
  let processedDueDate = parameters.dueDate

  // 如果任务标题或内容包含相对时间表达，进行智能转换
  const taskText = `${parameters.title || ''} ${parameters.content || ''}`.toLowerCase()

  // 🎯 辅助函数：生成正确的时间格式（避免时区转换问题）
  const formatLocalDateTime = (targetDate, hour, minute = 0, second = 0) => {
    const year = targetDate.getFullYear()
    const month = String(targetDate.getMonth() + 1).padStart(2, '0')
    const day = String(targetDate.getDate()).padStart(2, '0')
    const hourStr = String(hour).padStart(2, '0')
    const minuteStr = String(minute).padStart(2, '0')
    const secondStr = String(second).padStart(2, '0')
    return `${year}-${month}-${day} ${hourStr}:${minuteStr}:${secondStr}`
  }

  // 时间智能处理逻辑
  if (!processedDueDate) {
    if (taskText.includes('今晚') || taskText.includes('今天晚上')) {
      processedDueDate = formatLocalDateTime(now, 23, 59, 59)
      console.log('🕒 [executeCreateTask] 检测到"今晚"，设置截止时间：', processedDueDate)
    } else if (taskText.includes('明天早上') || taskText.includes('明早')) {
      const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000)
      processedDueDate = formatLocalDateTime(tomorrow, 9, 0, 0)
      console.log('🕒 [executeCreateTask] 检测到"明天早上"，设置截止时间：', processedDueDate)
    } else if (taskText.includes('明天晚上') || taskText.includes('明晚')) {
      const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000)
      processedDueDate = formatLocalDateTime(tomorrow, 23, 59, 59)
      console.log('🕒 [executeCreateTask] 检测到"明天晚上"，设置截止时间：', processedDueDate)
    } else if (taskText.includes('明天')) {
      const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000)
      processedDueDate = formatLocalDateTime(tomorrow, 18, 0, 0)
      console.log('🕒 [executeCreateTask] 检测到"明天"，设置截止时间：', processedDueDate)
    } else if (taskText.includes('今天')) {
      processedDueDate = formatLocalDateTime(now, 18, 0, 0)
      console.log('🕒 [executeCreateTask] 检测到"今天"，设置截止时间：', processedDueDate)
    } else if (taskText.includes('本周末') || taskText.includes('周末')) {
      const currentWeekday = now.getDay()
      const daysToSaturday = 6 - currentWeekday
      const thisSaturday = new Date(now.getTime() + daysToSaturday * 24 * 60 * 60 * 1000)
      processedDueDate = formatLocalDateTime(thisSaturday, 18, 0, 0)
      console.log('🕒 [executeCreateTask] 检测到"本周末"，设置截止时间：', processedDueDate)
    } else if (taskText.includes('下周一')) {
      const currentWeekday = now.getDay()
      const daysToNextMonday = (8 - currentWeekday) % 7 || 7
      const nextMonday = new Date(now.getTime() + daysToNextMonday * 24 * 60 * 60 * 1000)
      processedDueDate = formatLocalDateTime(nextMonday, 18, 0, 0)
      console.log('🕒 [executeCreateTask] 检测到"下周一"，设置截止时间：', processedDueDate)
    }
  }

  // 构建 todo 模块的创建参数
  const options = {
    title: parameters.title,
    content: parameters.content || null,
    priority: parameters.priority || null,
    projectName: parameters.projectName || null,
    tagNames: parameters.tagNames || null,
    startDate: processedStartDate,
    dueDate: processedDueDate,
    isAllDay: parameters.isAllDay || null,
    reminder: parameters.reminder || null,
    kind: 'TEXT', // 默认为文本类型任务
  }

  console.log('⚙️ [executeCreateTask] 处理后的创建参数：', JSON.stringify(options, null, 2))

  const result = await todoTool.createTask(options)

  if (result.errCode !== null && result.errCode !== 0) {
    throw new Error(`创建任务失败：${result.errMsg}`)
  }

  // 构建详细的成功消息
  let successMessage = `成功创建任务：${parameters.title}`
  if (processedDueDate && processedDueDate !== parameters.dueDate) {
    successMessage += `，已智能设置截止时间为：${processedDueDate}`
  }

  console.log('✅ [executeCreateTask] === 任务创建完成 ===')
  console.log('📝 [executeCreateTask] 任务信息：', {
    title: parameters.title,
    originalDueDate: parameters.dueDate,
    processedDueDate: processedDueDate,
    智能时间处理: processedDueDate !== parameters.dueDate,
  })

  return {
    success: true,
    data: {
      title: result.data.title,
      status: result.data.status,
    },
    message: `任务"${result.data.title}"已创建成功！`,
    needsReminder: true, // 提示用户可以设置提醒
  }
}

// 获取项目列表
async function executeGetProjects(parameters) {
  const todoTool = new TodoTool()

  // 构建 todo 模块的查询参数
  const options = {
    keyword: parameters.keyword || null,
    includeClosed: parameters.includeClosed || false,
  }

  const result = await todoTool.getProjects(options)

  if (result.errCode !== 0) {
    throw new Error(`获取项目失败：${result.errMsg}`)
  }

  return {
    success: true,
    data: result.data,
    message: `成功获取 ${result.data?.length || 0} 个项目`,
  }
}

// 更新任务
async function executeUpdateTask(parameters) {
  const todoTool = new TodoTool()

  // 构建 todo 模块的更新数据
  const updateData = {}

  // 只添加明确指定的字段
  if (parameters.title !== undefined) updateData.title = parameters.title
  if (parameters.content !== undefined) updateData.content = parameters.content
  if (parameters.priority !== undefined) updateData.priority = parameters.priority
  if (parameters.projectName !== undefined) updateData.projectName = parameters.projectName
  if (parameters.completed !== undefined) {
    // 根据完成状态设置 status 字段
    updateData.status = parameters.completed ? 2 : 0
  }
  if (parameters.dueDate !== undefined) updateData.dueDate = parameters.dueDate
  if (parameters.startDate !== undefined) updateData.startDate = parameters.startDate

  const result = await todoTool.updateTask(parameters.taskId, updateData)

  if (result.errCode !== null && result.errCode !== 0) {
    throw new Error(`更新任务失败：${result.errMsg}`)
  }

  return {
    success: true,
    data: result.data,
    message: `成功更新任务`,
  }
}

// 获取当前时间信息
async function executeGetCurrentTimeInfo(parameters) {
  console.log('🕒 [executeGetCurrentTimeInfo] === 开始获取当前时间信息 ===')

  const now = new Date()
  const format = parameters.format || 'detailed'

  // 计算相对时间
  const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000)
  const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000)
  const dayAfterTomorrow = new Date(now.getTime() + 2 * 24 * 60 * 60 * 1000)
  const dayBeforeYesterday = new Date(now.getTime() - 2 * 24 * 60 * 60 * 1000)
  const nextWeek = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000)

  // 计算本周和下周的日期
  const currentWeekday = now.getDay() // 0=周日，1=周一，..., 6=周六
  const daysToSaturday = 6 - currentWeekday
  const daysToSunday = 7 - currentWeekday
  const thisSaturday = new Date(now.getTime() + daysToSaturday * 24 * 60 * 60 * 1000)
  const thisSunday = new Date(now.getTime() + daysToSunday * 24 * 60 * 60 * 1000)

  // 计算下周一
  const daysToNextMonday = (8 - currentWeekday) % 7 || 7
  const nextMonday = new Date(now.getTime() + daysToNextMonday * 24 * 60 * 60 * 1000)

  // 计算本月和下月
  const currentMonth = now.getMonth()
  const currentYear = now.getFullYear()
  const lastDayOfMonth = new Date(currentYear, currentMonth + 1, 0)
  const firstDayOfNextMonth = new Date(currentYear, currentMonth + 1, 1)
  const lastDayOfNextMonth = new Date(currentYear, currentMonth + 2, 0)

  let timeData = {}

  switch (format) {
    case 'iso':
      timeData = {
        current_datetime: now.toISOString(),
        today: now.toISOString().split('T')[0],
        tomorrow: tomorrow.toISOString().split('T')[0],
        yesterday: yesterday.toISOString().split('T')[0],
      }
      break

    case 'local':
      timeData = {
        current_date: now.toLocaleDateString('zh-CN'),
        current_time: now.toLocaleTimeString('zh-CN'),
        today: now.toLocaleDateString('zh-CN'),
        tomorrow: tomorrow.toLocaleDateString('zh-CN'),
        yesterday: yesterday.toLocaleDateString('zh-CN'),
      }
      break

    case 'detailed':
    default:
      timeData = {
        // 基础时间信息
        current_datetime: now.toISOString(),
        current_date: now.toLocaleDateString('zh-CN'),
        current_time: now.toLocaleTimeString('zh-CN'),
        current_year: now.getFullYear(),
        current_month: now.getMonth() + 1,
        current_day: now.getDate(),
        current_hour: now.getHours(),
        current_minute: now.getMinutes(),
        current_weekday: ['周日', '周一', '周二', '周三', '周四', '周五', '周六'][now.getDay()],
        timezone: 'Asia/Shanghai (UTC+8)',
        unix_timestamp: now.getTime(),

        // 相对时间转换
        relative_times: {
          今天: now.toLocaleDateString('zh-CN'),
          明天: tomorrow.toLocaleDateString('zh-CN'),
          后天: dayAfterTomorrow.toLocaleDateString('zh-CN'),
          昨天: yesterday.toLocaleDateString('zh-CN'),
          前天: dayBeforeYesterday.toLocaleDateString('zh-CN'),
          今晚: `${now.toLocaleDateString('zh-CN')} 23:59:59`,
          明天早上: `${tomorrow.toLocaleDateString('zh-CN')} 09:00:00`,
          明天晚上: `${tomorrow.toLocaleDateString('zh-CN')} 23:59:59`,
          本周末: thisSaturday.toLocaleDateString('zh-CN'),
          这个周末: thisSunday.toLocaleDateString('zh-CN'),
          下周一: nextMonday.toLocaleDateString('zh-CN'),
          下周: nextWeek.toLocaleDateString('zh-CN'),
          本月底: `${lastDayOfMonth.toLocaleDateString('zh-CN')} 23:59:59`,
          下个月初: `${firstDayOfNextMonth.toLocaleDateString('zh-CN')} 09:00:00`,
          下月底: `${lastDayOfNextMonth.toLocaleDateString('zh-CN')} 23:59:59`,
        },

        // 格式化示例
        format_examples: {
          'YYYY-MM-DD': now.toISOString().split('T')[0],
          'YYYY-MM-DD HH:MM:SS': `${now.toISOString().split('T')[0]} ${now.toTimeString().split(' ')[0]}`,
          'MM-DD': `${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}`,
          中文日期: now.toLocaleDateString('zh-CN'),
        },
      }
      break
  }

  console.log('📅 [executeGetCurrentTimeInfo] 生成的时间数据：', JSON.stringify(timeData, null, 2))
  console.log('✅ [executeGetCurrentTimeInfo] === 获取当前时间信息完成 ===')

  return {
    success: true,
    data: timeData,
    message: `成功获取当前时间信息（格式：${format}）`,
    current_time: now.toLocaleString('zh-CN'),
  }
}